<?php

namespace App\Livewire\Form;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceSingleOrderItem;
use App\States\OrderItem\ContentAssignedToWriter;
use Domain\Order\Modules\StatesTraits;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

/*********************************************************************
 * CONTENT FORM COMPONENT
 *********************************************************************
 *
 * Handles content creation and updates for marketplace order items.
 * Manages both team-written and customer-provided content.
 *
 * Features:
 * - Content source selection (team/customer)
 * - Media attachment handling
 * - Content state transitions
 * - Form validation
 *
 * @property MarketplaceSingleOrderItem $orderItem
 * @property string|null $title
 * @property string|null $content_body
 * @property string|null $content_url
 * @property string|null $comments
 * @property int $content_source
 * @property array $media_ids
 * @property bool $disable_fields
 *
 *********************************************************************/
class ContentForm extends Component
{
    use StatesTraits;
    use WithFileUploads;

    public MarketplaceSingleOrderItem $orderItem;

    public string|null $title = null;
    public string|null $content_body = null;
    public string|null $content_url = null;
    public string|null $comments = null;
    public int $content_source = 0; // 0 = team, 1 = customer
    public array $media_ids = [];

    public bool $disable_fields = false;


    /*********************************************************************
     * MOUNT COMPONENT
     *********************************************************************
     *
     * Initialize component with order item data.
     * Sets initial values for content fields if content exists.
     * Determines if fields should be disabled based on order state.
     *
     * @return void
     *
     *********************************************************************/
    public function mount(): void
    {
        $this->content_source = $this->orderItem->is_content_provided_by_customer;

        if ($this->orderItem->content) {
            $this->title = $this->orderItem->content->title;
            $this->content_body = $this->orderItem->content->content_body;
            $this->content_url = $this->orderItem->content->content_url;
            $this->comments = $this->orderItem->content->comments;
            $this->media_ids = $this->orderItem->content->media()->pluck('id')->toArray();
        }

        // -----------------------
        // Set Field Disable State
        $this->disable_fields = !$this->canEditContent();
    }


    /*********************************************************************
     * VALIDATION RULES
     *********************************************************************
     *
     * Define validation rules for content form fields.
     *
     * @return array Validation rules
     *
     *********************************************************************/
    public function rules(): array
    {
        return [
            'content_source' => 'required|integer|in:0,1',
            'title' => 'required_if:content_source,1|string|max:255',
            'content_body' => 'required_if:content_source,1|string',
            'content_url' => 'nullable|url|max:255',
            'comments' => 'nullable|string|max:1000|required_if:content_source,1',
            'media_ids' => 'array',
            'media_ids.*' => 'exists:media,id',
        ];
    }


    /*********************************************************************
     * HANDLE MEDIA UPLOAD
     *********************************************************************
     *
     * Add newly uploaded media to the media_ids array.
     *
     * @param int $mediaId ID of the uploaded media
     * @return void
     *
     *********************************************************************/
    #[On('media-uploaded')]
    public function handleMediaUploaded($mediaId): void
    {
        if (! in_array($mediaId, $this->media_ids, true)) {
            $this->media_ids[] = $mediaId;
        }
    }


    /*********************************************************************
     * HANDLE MEDIA REMOVAL
     *********************************************************************
     *
     * Remove media from the media_ids array.
     *
     * @param int $mediaId ID of the media to remove
     * @return void
     *
     *********************************************************************/
    #[On('media-removed')]
    public function handleMediaRemoved($mediaId): void
    {
        $this->media_ids = array_filter($this->media_ids, fn($id) => $id !== $mediaId);
    }


    /*********************************************************************
     * REMOVE MEDIA
     *********************************************************************
     *
     * Remove media and update content files array.
     *
     * @param int $mediaId ID of the media to remove
     * @return void
     *
     *********************************************************************/
    public function MediaRemove($mediaId): void
    {
        // -----------------------
        // Remove Media ID
        $this->media_ids = array_filter($this->media_ids, fn($id) => $id !== $mediaId);

        // -----------------------
        // Update Content Files
        if ($this->orderItem->content) {
            $this->orderItem->content->update([
                'files_array' => $this->media_ids,
            ]);
        }
    }


    /*********************************************************************
     * WRITE CONTENT
     *********************************************************************
     *
     * Create initial content entry for team-written content.
     * Transitions order item to ContentAssignedToWriter state.
     *
     * @return void
     *
     *********************************************************************/
    public function writeContent(): void
    {
        // -----------------------
        // Create Content Entry
        $validated = [
            'title' => '',
            'content_body' => '',
            'content_url' => '',
            'media_ids' => [],
            'comments' => '',
            'content_source' => $this->content_source === 1 ? 'customer' : 'team',
            'writer_id' => 1,
        ];

        $this->transitionState(OrderItemStates::ContentAssignedToWriter, $this->orderItem, $validated);

        $this->notifySuccessAndReload('Content submitted successfully.');
    }


    /*********************************************************************
     * UPDATE CONTENT
     *********************************************************************
     *
     * Update or create content with validation.
     * Handles media attachment and state transitions.
     *
     * @return void
     *
     *********************************************************************/
    public function updateContent(): void
    {

        $validated = $this->validate();

        try {

            $this->transitionState(OrderItemStates::ContentAwaitingPublisherApproval, $this->orderItem, $validated);

            // -----------------------
            // Reset Form
            $this->reset(['title', 'content_body', 'content_url', 'comments', 'media_ids']);
            $this->notifySuccessAndReload('Content submitted successfully.');
        } catch (\Exception $e) {
            Log::error('Failed to update content: order item id ' . $this->orderItem->id . ' ' . $e->getMessage());
            $this->js(expression: "toast('Content submission failed', {type: 'error', position: 'bottom-center'})");
        }
    }


    /*********************************************************************
     * SUBMIT CONTENT
     *********************************************************************
     *
     * Handle content submission based on source.
     * Routes to appropriate handler based on content source.
     * Prevents submission if fields are disabled.
     *
     * @return void
     *
     *********************************************************************/
    public function submit(): void
    {
        // -----------------------
        // Check If Submission Allowed
        if ($this->disable_fields) {
            $this->js("toast('Content cannot be edited in current state.', {type: 'error', position: 'bottom-center'})");
            return;
        }

        if ($this->content_source === 0) {
            $this->writeContent();
        } else {
            $this->updateContent();
        }
    }



    /*********************************************************************
     * CAN EDIT CONTENT
     *********************************************************************
     *
     * Determine if content can be edited based on order item state.
     * Admins and superadmins can always edit content.
     * Other users can only edit in specific states.
     *
     * @return bool True if content can be edited, false otherwise
     *
     *********************************************************************/
    private function canEditContent(): bool
    {
        // -----------------------
        // Admin Override
        if (auth()->user()->hasRole(['admin', 'superadmin'])) {
            return true;
        }

        // -----------------------
        // Check Allowed States
        $allowedStates = [
            OrderItemStates::ContentPending->value,
            OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            OrderItemStates::ContentAssignedToWriter->value,
        ];

        return in_array($this->orderItem->state_name, $allowedStates);
    }


     /*********************************************************************
     * NOTIFY SUCCESS AND RELOAD
     *********************************************************************
     *
     * Show success toast and reload page.
     *
     * @param string $message Success message to display
     * @return void
     *
     *********************************************************************/
    private function notifySuccessAndReload(string $message): void
    {
        $this->js("toast('{$message}', {type: 'success', position: 'bottom-center'})");
        $this->js('setTimeout(() => window.location.reload(), 1000);');
    }


    /*********************************************************************
     * UPDATE DISABLE STATE
     *********************************************************************
     *
     * Update the disable_fields state based on current order item state.
     * Called when order item state might have changed.
     *
     * @return void
     *
     *********************************************************************/
    public function updateDisableState(): void
    {
        $this->disable_fields = !$this->canEditContent();
    }


    /*********************************************************************
     * RENDER COMPONENT
     *********************************************************************
     *
     * Render the content form view.
     * Updates disable state before rendering.
     *
     * @return \Illuminate\View\View
     *
     *********************************************************************/
    public function render()
    {
        // -----------------------
        // Update Disable State
        $this->updateDisableState();

        return view('livewire.form.content-form');
    }
}
